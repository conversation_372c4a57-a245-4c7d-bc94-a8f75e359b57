#!/usr/bin/env python3
"""
Test script for the Collision function to verify it works correctly.
"""

import numpy as np

# Define the collision detection functions directly to avoid vedo import issues
def get_line_pixels(p1, p2):
    """
    Get all pixels on the discrete grid line from p1 to p2.
    Uses <PERSON><PERSON>enham's line algorithm for precise line rasterization.
    p1, p2 are 2-element numpy arrays (x, y).
    """
    x1, y1 = int(p1[0]), int(p1[1])
    x2, y2 = int(p2[0]), int(p2[1])

    pixels = []

    # Handle zero-length path (same point)
    if x1 == x2 and y1 == y2:
        return np.array([[x1, y1]], dtype=int)

    # Bresenham's line algorithm
    dx = abs(x2 - x1)
    dy = abs(y2 - y1)

    x, y = x1, y1

    x_inc = 1 if x1 < x2 else -1
    y_inc = 1 if y1 < y2 else -1

    error = dx - dy

    while True:
        pixels.append([x, y])

        if x == x2 and y == y2:
            break

        e2 = 2 * error

        if e2 > -dy:
            error -= dy
            x += x_inc

        if e2 < dx:
            error += dx
            y += y_inc

    return np.array(pixels, dtype=int)

def Collision(img, x1, x2):
    """
    Check if the line segment from x1 to x2 collides with obstacles in img.

    Parameters:
    - img: 2D boolean numpy array where True indicates obstacle, False indicates free space
    - x1: 2D numpy array [x, y] representing starting point coordinates
    - x2: 2D numpy array [x, y] representing ending point coordinates

    Returns:
    - True if path intersects with ANY obstacles or goes out of bounds
    - False if path is completely clear

    This version uses robust line rasterization to check ALL pixels along the path.
    """
    # Validate inputs
    if img is None or len(img.shape) != 2:
        return True  # Invalid map is considered a collision

    height, width = img.shape

    # Convert inputs to numpy arrays if they aren't already
    x1 = np.array(x1, dtype=float)
    x2 = np.array(x2, dtype=float)

    # Check if endpoints are within bounds first
    if (x1[0] < 0 or x1[0] >= width or x1[1] < 0 or x1[1] >= height or
        x2[0] < 0 or x2[0] >= width or x2[1] < 0 or x2[1] >= height):
        return True  # Endpoints out of bounds

    # Check if endpoints are on obstacles
    if (img[int(x1[1]), int(x1[0])] or img[int(x2[1]), int(x2[0])]):
        return True  # Endpoint on obstacle

    # Get all pixels that the line from x1 to x2 touches
    line_pixels = get_line_pixels(x1, x2)

    # Check each pixel along the path
    for p in line_pixels:
        px, py = p[0], p[1]

        # Check 1: Is the pixel outside the map boundaries?
        if px < 0 or px >= width or py < 0 or py >= height:
            return True  # Path goes out of bounds, this is a collision

        # Check 2: Is the pixel on an obstacle? (img is indexed [y, x])
        if img[py, px]:
            return True  # Path collides with an obstacle

    return False  # No collision found

def create_test_map():
    """Create a simple test map with obstacles."""
    # Create a 20x20 map
    img = np.zeros((20, 20), dtype=bool)
    
    # Add some obstacles (True = obstacle)
    img[5:15, 8:12] = True  # Vertical wall
    img[8:12, 5:15] = True  # Horizontal wall
    img[2:4, 2:4] = True    # Small obstacle
    img[16:18, 16:18] = True # Corner obstacle
    
    return img

def test_collision_detection():
    """Test various collision scenarios."""
    print("Testing Collision Detection Function")
    print("=" * 40)
    
    # Create test map
    img = create_test_map()
    
    # Test cases
    test_cases = [
        # (start_point, end_point, expected_result, description)
        ([0, 0], [1, 1], False, "Clear short diagonal path"),
        ([0, 0], [10, 10], True, "Path through obstacle"),
        ([0, 0], [2, 2], True, "Path to small obstacle"),
        ([1, 1], [1, 1], False, "Zero-length path in free space"),
        ([9, 9], [9, 9], True, "Zero-length path on obstacle"),
        ([0, 0], [19, 19], True, "Long diagonal through obstacles"),
        ([0, 0], [19, 0], False, "Horizontal path along bottom edge"),
        ([0, 0], [0, 19], False, "Vertical path along left edge"),
        ([-1, 0], [5, 5], True, "Start point out of bounds"),
        ([5, 5], [25, 25], True, "End point out of bounds"),
        ([15, 0], [15, 19], False, "Vertical path in free space"),
        ([0, 10], [19, 10], True, "Horizontal path through obstacle"),
        ([0, 15], [19, 15], False, "Horizontal path in free space"),
        ([4, 4], [4, 19], False, "Vertical path avoiding obstacles"),
        ([0, 4], [19, 4], False, "Horizontal path below obstacles"),
    ]
    
    passed = 0
    total = len(test_cases)
    
    for i, (start, end, expected, description) in enumerate(test_cases):
        start_np = np.array(start)
        end_np = np.array(end)
        
        result = Collision(img, start_np, end_np)
        status = "PASS" if result == expected else "FAIL"
        
        print(f"Test {i+1:2d}: {status} - {description}")
        print(f"         Start: {start}, End: {end}")
        print(f"         Expected: {expected}, Got: {result}")
        
        if result == expected:
            passed += 1
        else:
            # Show the line pixels for failed tests
            pixels = get_line_pixels(start_np, end_np)
            print(f"         Line pixels: {pixels.tolist()}")
        
        print()
    
    print(f"Results: {passed}/{total} tests passed")
    return passed == total

def visualize_test_map():
    """Visualize the test map and some test paths using simple text output."""
    img = create_test_map()

    print("\nTest Map Visualization (# = obstacle, . = free space):")
    print("=" * 50)

    # Print the map
    for y in range(img.shape[0] - 1, -1, -1):  # Print from top to bottom
        row = ""
        for x in range(img.shape[1]):
            if img[y, x]:
                row += "# "
            else:
                row += ". "
        print(f"{y:2d} {row}")

    # Print x-axis labels
    x_labels = "   "
    for x in range(img.shape[1]):
        x_labels += f"{x%10} "
    print(x_labels)

    print("\nTesting some example paths:")
    print("-" * 30)

    # Test paths
    test_paths = [
        ([0, 0], [3, 3], 'Clear diagonal path'),
        ([0, 0], [10, 10], 'Path through obstacle'),
        ([15, 0], [15, 19], 'Vertical path in free space'),
        ([0, 15], [19, 15], 'Horizontal path through obstacle'),
    ]

    for start, end, description in test_paths:
        start_np = np.array(start)
        end_np = np.array(end)

        # Get line pixels
        pixels = get_line_pixels(start_np, end_np)

        # Check collision
        collision = Collision(img, start_np, end_np)
        status = "COLLISION" if collision else "CLEAR"

        print(f"{description}:")
        print(f"  Path: {start} -> {end}")
        print(f"  Pixels: {len(pixels)} points")
        print(f"  Result: {status}")
        print()

if __name__ == "__main__":
    # Run tests
    success = test_collision_detection()
    
    if success:
        print("\n✅ All collision detection tests passed!")
    else:
        print("\n❌ Some tests failed. Check the implementation.")
    
    # Create visualization
    visualize_test_map()
