#!/usr/bin/env python3
"""
Simple test to check the obstacle map processing.
"""

import numpy as np
from PIL import Image
import os

# Load the image
imagePath = 'obstacle_map.png'

if os.path.exists(imagePath):
    print(f"Loading {imagePath}...")
    img_pil = Image.open(imagePath).convert('L')
    img_np = np.array(img_pil)
    img = img_np < 128  # Same logic as main program
    
    print(f"Image shape: {img.shape}")
    print(f"Image dtype: {img.dtype}")
    print(f"Total pixels: {img.size}")
    print(f"Obstacle pixels (True): {np.sum(img)}")
    print(f"Free pixels (False): {np.sum(~img)}")
    print(f"Percentage obstacles: {100 * np.sum(img) / img.size:.1f}%")
    
    # Show a small corner
    print(f"\nTop-left 10x10 corner:")
    corner = img[:10, :10]
    for y in range(9, -1, -1):
        row = f"{y} "
        for x in range(10):
            row += "#" if corner[y, x] else "."
        print(row)
    print("  0123456789")
    
    # Test a simple collision
    print(f"\nTesting simple collision detection...")
    
    # Simple collision function
    def simple_collision_test(img, x1, y1, x2, y2):
        """Very simple collision test."""
        # Check if endpoints are obstacles
        if img[y1, x1] or img[y2, x2]:
            return True, "Endpoint on obstacle"
        
        # Simple line check - just check a few points along the line
        steps = 10
        for i in range(steps + 1):
            t = i / steps
            x = int(x1 + t * (x2 - x1))
            y = int(y1 + t * (y2 - y1))
            if 0 <= x < img.shape[1] and 0 <= y < img.shape[0]:
                if img[y, x]:
                    return True, f"Obstacle at ({x},{y})"
            else:
                return True, f"Out of bounds at ({x},{y})"
        
        return False, "Clear path"
    
    # Test some paths
    test_cases = [
        (0, 0, 5, 5, "Diagonal in corner"),
        (0, 0, 50, 50, "Longer diagonal"),
        (100, 100, 200, 200, "Middle area diagonal"),
    ]
    
    for x1, y1, x2, y2, desc in test_cases:
        if (0 <= x1 < img.shape[1] and 0 <= y1 < img.shape[0] and 
            0 <= x2 < img.shape[1] and 0 <= y2 < img.shape[0]):
            collision, reason = simple_collision_test(img, x1, y1, x2, y2)
            print(f"Path ({x1},{y1}) -> ({x2},{y2}) [{desc}]: {'COLLISION' if collision else 'CLEAR'} - {reason}")
        else:
            print(f"Path ({x1},{y1}) -> ({x2},{y2}) [{desc}]: OUT OF BOUNDS")

else:
    print(f"Error: {imagePath} not found!")
    
print("Test complete.")
