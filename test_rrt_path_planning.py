#!/usr/bin/env python3
"""
Test script for RRT path planning functionality
Tests the core components without GUI interaction
"""

import numpy as np
from PIL import Image
import sys
import os

# Import the RRT functions
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Test the core RRT functionality
def test_rrt_path_planning():
    print("🧪 Testing RRT Path Planning Components")
    print("=" * 50)
    
    # Load the obstacle map
    try:
        img = np.array(Image.open('obstacle_map.png').convert('L')) > 128
        print(f"✅ Obstacle map loaded: {img.shape}")
        print(f"   Obstacle coverage: {(~img).sum() / img.size * 100:.1f}%")
    except Exception as e:
        print(f"❌ Failed to load obstacle map: {e}")
        return False
    
    # Test collision detection
    from rrt_with_collision import Collision
    
    # Test case 1: Clear path
    start = np.array([50, 50])
    end = np.array([100, 50])
    collision = Collision(img, start, end)
    print(f"✅ Collision detection test 1 (clear path): {'No collision' if not collision else 'Collision detected'}")
    
    # Test case 2: Path through obstacle
    start = np.array([50, 50])
    end = np.array([200, 200])  # This should hit obstacles
    collision = Collision(img, start, end)
    print(f"✅ Collision detection test 2 (obstacle path): {'Collision detected' if collision else 'No collision'}")
    
    # Test Sample class
    from rrt_with_collision import Sample
    
    source = np.array([50, 50])
    sample1 = Sample(source)
    sample2 = Sample(np.array([60, 60]), 0)  # Parent is sample1 (index 0)
    
    print(f"✅ Sample creation test:")
    print(f"   Source sample: {sample1.x}, parent: {sample1.parent}")
    print(f"   Child sample: {sample2.x}, parent: {sample2.parent}")

    # Test path tracing logic
    samples = [sample1, sample2]

    # Simulate path tracing
    successful_path = []
    current_index = 1  # Start from the goal node (sample2)

    while current_index is not None:
        current_sample = samples[current_index]
        successful_path.append(current_sample.x)
        current_index = current_sample.parent
    
    successful_path.reverse()
    print(f"✅ Path tracing test:")
    print(f"   Path: {[tuple(p) for p in successful_path]}")
    
    # Test goal distance calculation
    destination = np.array([65, 65])
    goal_threshold = 15
    
    distance_to_goal = np.linalg.norm(sample2.x - destination)
    goal_reached = distance_to_goal <= goal_threshold
    
    print(f"✅ Goal reaching test:")
    print(f"   Sample position: {sample2.x}")
    print(f"   Destination: {destination}")
    print(f"   Distance: {distance_to_goal:.1f}")
    print(f"   Goal threshold: {goal_threshold}")
    print(f"   Goal reached: {goal_reached}")
    
    print("\n🎉 All core components tested successfully!")
    print("\nTo test the full GUI application:")
    print("1. Run: python rrt_with_collision.py")
    print("2. Click to set source point (black dot)")
    print("3. Click again to set destination point (red dot)")
    print("4. Press 'Run' button to start RRT path planning")
    print("5. Watch as the algorithm finds a path!")
    
    return True

if __name__ == "__main__":
    test_rrt_path_planning()
