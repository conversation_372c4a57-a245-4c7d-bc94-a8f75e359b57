#%%
from scipy.spatial import KD<PERSON>ree
import numpy as np
import math
import random
import vedo as vd
from vedo.pyplot import DirectedGraph
import os
from PIL import Image
from vedo import Text2D

vd.settings.default_backend = 'vtk'

#%%
# --- Sample Class ---
class Sample:
    def __init__(self, x, parent=None):
        self.x = x  # Node position (2D)
        self.parent = parent  # Index of parent node

# --- KDTree Nearest Neighbor ---
def NearestSample(samples, x):
    """Find the nearest sample in the tree to point x."""
    tree = KDTree([sample.x for sample in samples])
    dist, sid = tree.query(x)
    return sid

# --- Collision Checking (to be implemented) ---
def Collision(img, x1, x2):
    """Check if the line from x1 to x2 collides with obstacles in img."""
    pass  # TODO: Implement collision checking

# --- RRT Sampling ---
def getNextSample(dim, samples, stepsize):
    """Sample a random point and step from nearest node towards it."""
    x_rand = np.array([random.randint(0, d-1) for d in dim])
    sid = NearestSample(samples, x_rand)
    x_nearest = np.array(samples[sid].x)
    direction = x_rand - x_nearest
    norm = np.linalg.norm(direction)
    if norm == 0:
        return None  # Random point is exactly at a node
    direction = direction / norm
    x_new = np.round(x_nearest + stepsize * direction).astype(int)
    x_new = np.clip(x_new, [0, 0], np.array(dim)-1)
    ns = Sample(x_new, sid)
    return ns

#%%
# --- Button Callback for Run/Pause ---
def bfunc(obj, ename):
    global timer_id
    if 'Run' in button.status():
        if timer_id == -1:
            timer_id = plt.timer_callback('create', dt=10)
            print('RRT running...')
    else:
        if timer_id != -1:
            plt.timer_callback('destroy', timer_id)
            timer_id = -1
            print('RRT paused.')
    button.switch()

# --- RRT Iteration Step ---
def doRRTIteration(event):
    y, x = img.shape  # Note: y,x order due to image convention
    ns = getNextSample([x, y], samples, stepSize)
    if ns is None:
        return
    # Check for collision from parent to new sample
    if Collision(img, ns.x, dest):
        return
    samples.append(ns)
    # Draw new node and edge
    points.vertices = np.vstack([points.vertices, np.hstack([ns.x, 0])])
    edges.append(vd.Line(ns.x, samples[ns.parent].x, lw=2))
    plt.add(points)
    plt.remove('Line')
    plt.add(edges)
    plt.render()
    # TODO: Add goal reached check

#%%
# --- Utility and GUI Button Functions ---
def reset_tree():
    """Reset the RRT tree to the source node only."""
    global samples, points, edges, setting_source
    samples.clear()
    samples.append(Sample(source))
    points = vd.Points([source], r=8)
    edges.clear()
    plt.remove('Line')
    plt.add(points)
    plt.render()
    setting_source = True
    print("Tree reset. Click to set source.")

#%%
# --- Visualization Enhancements ---
tree_colors = ['red', 'blue', 'green']
tree_color_idx = 0
show_edges = True

def toggle_tree_color(obj, ename):
    """Cycle through tree edge colors."""
    global tree_color_idx, edges
    tree_color_idx = (tree_color_idx + 1) % len(tree_colors)
    for edge in edges:
        edge.c(tree_colors[tree_color_idx])
    plt.add(edges)
    plt.render()
    obj.switch()
    print(f"Tree color: {tree_colors[tree_color_idx]}")

def toggle_tree_visibility(obj, ename):
    """Show or hide the tree edges."""
    global show_edges, edges
    show_edges = not show_edges
    if show_edges:
        plt.add(edges)
        obj.status("Hide Tree")
        print("Tree edges: Visible")
    else:
        for edge in edges:
            plt.remove(edge)
        obj.status("Show Tree")
        print("Tree edges: Hidden")
    plt.render()

#%%
# --- Main GUI and State Initialization ---
imagePath = 'obstacle_map.png'
img_pil = Image.open(imagePath)
img_pil.save(imagePath)  # Ensure grayscale PNG

source = np.array([0, 0])
dest = np.array([50, 50])
samples = [Sample(source)]
points = vd.Points([source], r=8)
edges = []

# Load and prepare obstacle image as 3-channel
vd_img = vd.Image(imagePath).bw().binarize()
img = vd_img.tonumpy().astype(bool)
img_rgb = np.stack([img.astype(np.uint8)*255]*3, axis=-1)
vd_img = vd.Image(img_rgb)
img_actor = vd_img  # Keep reference to the current image actor

plt = vd.Plotter()
plt += img_actor
plt.user_mode('2d')
button = plt.add_button(bfunc, states=[" Run ", "Pause"], pos=(0.05, 0.34), size=24)
plt.add_button(lambda obj, ename: reset_tree(), pos=(0.05, 0.28), size=24, states=["Reset"])
plt.add_button(toggle_tree_color, pos=(0.05, 0.42), size=24, states=["Tree Color"])
plt.add_button(toggle_tree_visibility, pos=(0.05, 0.5), size=24, states=["Hide Tree", "Show Tree"])

stepSize = 10  # Default step size for RRT

def on_slider(widget, event):
    """Update the RRT step size from the slider."""
    global stepSize
    stepSize = int(widget.value)
    print(f"Step size set to {stepSize}")

plt.add_slider(
    on_slider,
    1, 50, stepSize,
    pos=((0.1, 0.9), (0.3, 0.9)),
    title="Step Size",
    c='blue',
    show_value=True
)

# --- Mouse click handling for both triangle drawing and source/destination setting ---
def on_click(evt):
    global source, dest, samples, points, edges, setting_source, draw_mode, triangle_points, img, img_actor, plt
    if evt.picked3d is None:
        return
    x, y = int(evt.picked3d[0]), int(evt.picked3d[1])
    # Only handle source/destination setting
    if setting_source:
        source = np.array([x, y])
        samples.clear()
        samples.append(Sample(source))
        points = vd.Points([source], r=8)
        edges.clear()
        plt.remove('Line')
        plt.add(points)
        plt += vd.Point(source, r=14, c='green')
        plt.render()
        setting_source = False
        print(f"Source set to {source}. Now click to set destination.")
    else:
        dest = np.array([x, y])
        plt += vd.Point(dest, r=14, c='red')
        plt.render()
        print(f"Destination set to {dest}. Press 'Run' to start.")
        setting_source = True


# --- Drawing Tool: Obstacle Editing ---
def draw_obstacle(evt):
    global img, img_actor
    if not draw_mode or evt.picked3d is None:
        return
    if hasattr(evt, 'isDragging') and not evt.isDragging:
        return
    if hasattr(evt, 'button') and evt.button != 0:
        return
    x, y = int(evt.picked3d[0]), int(evt.picked3d[1])
    yy, xx = np.ogrid[:img.shape[0], :img.shape[1]]
    mask = (xx - x)**2 + (yy - y)**2 <= brush_radius**2
    img[yy, xx][mask] = True
    img_rgb = np.stack([img.astype(np.uint8)*255]*3, axis=-1)
    img_actor = vd.Image(img_rgb)
    plt.remove('Image')
    plt += img_actor
    plt.render()

# --- Key press handling for drawing and brush size ---
def on_keypress(evt):
    global brush_radius
    if hasattr(evt, 'keyPressed'):
        key = evt.keyPressed.lower()
        if key == 'r':
            # Manual trigger for Run button
            if 'Run' in button.status():
                button.switch()
                print("Run triggered by keyboard.")
        elif key == '+':
            brush_radius = min(brush_radius + 2, 50)
            print(f"Brush radius: {brush_radius}")
        elif key == '-':
            brush_radius = max(1, brush_radius - 2)
            print(f"Brush radius: {brush_radius}")

# --- Button callback for toggling draw mode ---
def toggle_draw_mode(obj, ename):
    global draw_mode
    draw_mode = not draw_mode
    print(f"Draw mode {'ON' if draw_mode else 'OFF'} (use the button to toggle)")
    obj.switch()

plt.add_callback("mouse click", on_click)
plt.add_callback("mouse move", draw_obstacle)
evntid = plt.add_callback("timer", doRRTIteration, enable_picking=False)
plt.add_callback("key press", on_keypress)
plt.add_button(toggle_draw_mode, pos=(0.05, 0.58), size=24, states=["Draw Mode OFF", "Draw Mode ON"])
timer_id = -1

#%%
# --- GUI State Variables ---
draw_mode = False  # True if in draw mode
brush_radius = 5   # Radius of the brush for drawing obstacles
setting_source = True  # True: next click sets source, False: sets destination
timer_id = -1

# Highlight source and destination points
source_point = vd.Point(source, r=14, c='green')
dest_point = vd.Point(dest, r=14, c='red')
plt += source_point
plt += dest_point

print("Click to set source and destination points.")
print("Use the 'Draw Mode' button to toggle drawing obstacles. Use '+'/'-' to change brush size.")
plt.show(zoom="tightest").close()

