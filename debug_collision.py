#!/usr/bin/env python3
"""
Debug script to test collision detection with the actual obstacle map.
"""

import numpy as np
from PIL import Image
import os

# Load the same image processing logic as the main program
imagePath = 'obstacle_map.png'

if not os.path.exists(imagePath):
    print(f"Error: {imagePath} not found!")
    exit(1)

# Load and process the image exactly like the main program
img_pil = Image.open(imagePath).convert('L')
img_np = np.array(img_pil)
img = img_np < 128  # Pixels darker than 128 are considered obstacles

print(f"Image shape: {img.shape}")
print(f"Image dtype: {img.dtype}")
print(f"Obstacle pixels (True): {np.sum(img)}")
print(f"Free pixels (False): {np.sum(~img)}")

# Define collision detection functions directly to avoid import issues
def get_line_pixels(p1, p2):
    """Bresenham's line algorithm for line rasterization."""
    x1, y1 = int(p1[0]), int(p1[1])
    x2, y2 = int(p2[0]), int(p2[1])

    pixels = []

    if x1 == x2 and y1 == y2:
        return np.array([[x1, y1]], dtype=int)

    dx = abs(x2 - x1)
    dy = abs(y2 - y1)

    x, y = x1, y1
    x_inc = 1 if x1 < x2 else -1
    y_inc = 1 if y1 < y2 else -1
    error = dx - dy

    while True:
        pixels.append([x, y])
        if x == x2 and y == y2:
            break
        e2 = 2 * error
        if e2 > -dy:
            error -= dy
            x += x_inc
        if e2 < dx:
            error += dx
            y += y_inc

    return np.array(pixels, dtype=int)

def Collision(img, x1, x2):
    """Check collision between two points."""
    if img is None or len(img.shape) != 2:
        return True

    height, width = img.shape
    x1 = np.array(x1, dtype=float)
    x2 = np.array(x2, dtype=float)

    if (x1[0] < 0 or x1[0] >= width or x1[1] < 0 or x1[1] >= height or
        x2[0] < 0 or x2[0] >= width or x2[1] < 0 or x2[1] >= height):
        return True

    if (img[int(x1[1]), int(x1[0])] or img[int(x2[1]), int(x2[0])]):
        return True

    line_pixels = get_line_pixels(x1, x2)

    for p in line_pixels:
        px, py = p[0], p[1]
        if px < 0 or px >= width or py < 0 or py >= height:
            return True
        if img[py, px]:
            return True

    return False

def test_specific_paths():
    """Test some specific paths that should clearly hit obstacles."""
    
    print("\n" + "="*50)
    print("TESTING COLLISION DETECTION")
    print("="*50)
    
    # Find some obstacle locations
    obstacle_coords = np.where(img)
    if len(obstacle_coords[0]) > 0:
        # Get first few obstacle pixels
        obs_y, obs_x = obstacle_coords[0][:5], obstacle_coords[1][:5]
        print(f"Sample obstacle pixels (y,x): {list(zip(obs_y, obs_x))}")
        
        # Test path to an obstacle
        if len(obs_x) > 0:
            target_obstacle = [obs_x[0], obs_y[0]]
            start_point = [10, 10]  # Assume this is free space
            
            print(f"\nTest 1: Path from {start_point} to obstacle at {target_obstacle}")
            collision = Collision(img, np.array(start_point), np.array(target_obstacle))
            print(f"Collision detected: {collision}")
            
            # Show the line pixels
            pixels = get_line_pixels(np.array(start_point), np.array(target_obstacle))
            print(f"Line pixels: {pixels[:10]}...")  # Show first 10
            
            # Check each pixel manually
            print("Manual pixel check:")
            for i, (px, py) in enumerate(pixels[:5]):
                if 0 <= px < img.shape[1] and 0 <= py < img.shape[0]:
                    is_obstacle = img[py, px]
                    print(f"  Pixel ({px},{py}): {'OBSTACLE' if is_obstacle else 'FREE'}")
                else:
                    print(f"  Pixel ({px},{py}): OUT OF BOUNDS")
    
    # Test some obvious cases
    print(f"\nTest 2: Path through center of image")
    center_x, center_y = img.shape[1]//2, img.shape[0]//2
    start = [0, center_y]
    end = [img.shape[1]-1, center_y]
    
    collision = Collision(img, np.array(start), np.array(end))
    print(f"Horizontal path {start} -> {end}: {'COLLISION' if collision else 'CLEAR'}")
    
    # Check what's at the center
    print(f"Center pixel ({center_x},{center_y}): {'OBSTACLE' if img[center_y, center_x] else 'FREE'}")

def visualize_small_section():
    """Show a small section of the image to understand the obstacle layout."""
    print(f"\n" + "="*50)
    print("IMAGE SECTION VISUALIZATION")
    print("="*50)
    
    # Show a 20x20 section from top-left
    section = img[:20, :20]
    print("Top-left 20x20 section (# = obstacle, . = free):")
    for y in range(section.shape[0]-1, -1, -1):
        row = f"{y:2d} "
        for x in range(section.shape[1]):
            row += "# " if section[y, x] else ". "
        print(row)
    
    # Print x-axis
    x_labels = "   "
    for x in range(min(20, section.shape[1])):
        x_labels += f"{x%10} "
    print(x_labels)

if __name__ == "__main__":
    test_specific_paths()
    visualize_small_section()
