from scipy.spatial import KD<PERSON><PERSON>
import numpy as np
import math
import random
import vedo as vd
from vedo.pyplot import DirectedGraph
import os
from PIL import Image
from vedo import Text2D

# vedo backend setting
vd.settings.default_backend = 'vtk'

# --- Sample Class ---
class Sample:
    def __init__(self, x, parent=None, cost=0.0):
        self.x = x  # Node position (2D numpy array)
        self.parent = parent  # Index of parent node
        self.cost = cost  # Cumulative cost from source to this node

# --- KDTree Nearest Neighbor ---
def NearestSample(samples, x):
    """Find the nearest sample in the tree to point x."""
    if not samples:
        return None
    tree = KDTree([sample.x for sample in samples])
    dist, sid = tree.query(x)
    return sid

# --- ROBUST LINE RASTERIZATION ---
def get_line_pixels(p1, p2):
    """
    Get all pixels on the discrete grid line from p1 to p2.
    Uses Bresenham's line algorithm for precise line rasterization.
    p1, p2 are 2-element numpy arrays (x, y).
    """
    x1, y1 = int(p1[0]), int(p1[1])
    x2, y2 = int(p2[0]), int(p2[1])

    pixels = []

    # Handle zero-length path (same point)
    if x1 == x2 and y1 == y2:
        return np.array([[x1, y1]], dtype=int)

    # Bresenham's line algorithm
    dx = abs(x2 - x1)
    dy = abs(y2 - y1)

    x, y = x1, y1

    x_inc = 1 if x1 < x2 else -1
    y_inc = 1 if y1 < y2 else -1

    error = dx - dy

    while True:
        pixels.append([x, y])

        if x == x2 and y == y2:
            break

        e2 = 2 * error

        if e2 > -dy:
            error -= dy
            x += x_inc

        if e2 < dx:
            error += dx
            y += y_inc

    return np.array(pixels, dtype=int)

# --- COLLISION CHECKING USING LINE RASTERIZATION ---
def Collision(img, x1, x2):
    """
    Check if the line segment from x1 to x2 collides with obstacles in img.

    Parameters:
    - img: 2D boolean numpy array where True indicates obstacle, False indicates free space
    - x1: 2D numpy array [x, y] representing starting point coordinates
    - x2: 2D numpy array [x, y] representing ending point coordinates

    Returns:
    - True if path intersects with ANY obstacles or goes out of bounds
    - False if path is completely clear

    This version uses robust line rasterization to check ALL pixels along the path.
    """
    # Validate inputs
    if img is None or len(img.shape) != 2:
        return True  # Invalid map is considered a collision

    height, width = img.shape

    # Convert inputs to numpy arrays if they aren't already
    x1 = np.array(x1, dtype=float)
    x2 = np.array(x2, dtype=float)

    # IMPORTANT: Convert from graphics coordinates to image coordinates
    # Graphics: (0,0) at bottom-left, Y increases upward
    # Image: (0,0) at top-left, Y increases downward
    # So we need to flip the Y coordinates
    y1_img = height - 1 - x1[1]
    y2_img = height - 1 - x2[1]

    # Check if endpoints are within bounds first
    if (x1[0] < 0 or x1[0] >= width or y1_img < 0 or y1_img >= height or
        x2[0] < 0 or x2[0] >= width or y2_img < 0 or y2_img >= height):
        return True  # Endpoints out of bounds

    # Check if endpoints are on obstacles (using flipped Y coordinates)
    if (img[int(y1_img), int(x1[0])] or img[int(y2_img), int(x2[0])]):
        return True  # Endpoint on obstacle

    # Get all pixels that the line from x1 to x2 touches (using original coordinates)
    line_pixels = get_line_pixels(x1, x2)

    # Check each pixel along the path
    for p in line_pixels:
        px, py = p[0], p[1]

        # Convert graphics Y to image Y
        py_img = height - 1 - py

        # Check 1: Is the pixel outside the map boundaries?
        if px < 0 or px >= width or py_img < 0 or py_img >= height:
            return True  # Path goes out of bounds, this is a collision

        # Check 2: Is the pixel on an obstacle? (img is indexed [y, x])
        if img[py_img, px]:
            return True  # Path collides with an obstacle

    return False  # No collision found

# --- RRT* Helper Functions ---
def calculate_search_radius(num_nodes):
    """Calculate dynamic search radius for RRT*."""
    if num_nodes <= 1:
        return max_radius

    # RRT* formula: radius = min(max_radius, gamma * (log(n)/n)^(1/d))
    radius = gamma * (np.log(num_nodes) / num_nodes) ** (1.0 / dimension)
    return min(max_radius, radius)

def calculate_distance(point1, point2):
    """Calculate Euclidean distance between two points."""
    return np.linalg.norm(point1 - point2)

def find_nodes_in_radius(center_point, samples, radius):
    """Find all nodes within the given radius of the center point."""
    nodes_in_radius = []
    for i, sample in enumerate(samples):
        if calculate_distance(center_point, sample.x) <= radius:
            nodes_in_radius.append(i)
    return nodes_in_radius

def find_best_parent(new_point, samples, radius):
    """Find the best parent node within radius that minimizes cost to new_point."""
    nodes_in_radius = find_nodes_in_radius(new_point, samples, radius)

    if not nodes_in_radius:
        return None, float('inf')

    best_parent = None
    best_cost = float('inf')

    for node_idx in nodes_in_radius:
        parent_sample = samples[node_idx]
        # Calculate cost: parent's cost + distance to new point
        edge_cost = calculate_distance(parent_sample.x, new_point)
        total_cost = parent_sample.cost + edge_cost

        # Check if path is collision-free
        if not Collision(img, parent_sample.x, new_point):
            if total_cost < best_cost:
                best_cost = total_cost
                best_parent = node_idx

    return best_parent, best_cost

def rewire_tree(new_node_idx, samples, radius):
    """Rewire existing nodes through new node if it provides shorter paths."""
    new_sample = samples[new_node_idx]
    nodes_in_radius = find_nodes_in_radius(new_sample.x, samples, radius)

    rewired_count = 0
    for node_idx in nodes_in_radius:
        if node_idx == new_node_idx:
            continue

        existing_sample = samples[node_idx]
        # Calculate potential new cost through new_sample
        edge_cost = calculate_distance(new_sample.x, existing_sample.x)
        new_cost = new_sample.cost + edge_cost

        # If new path is better and collision-free, rewire
        if (new_cost < existing_sample.cost and
            not Collision(img, new_sample.x, existing_sample.x)):

            existing_sample.parent = new_node_idx
            existing_sample.cost = new_cost
            rewired_count += 1

            # Recursively update costs of all descendants
            update_descendant_costs(node_idx, samples)

    return rewired_count

def update_descendant_costs(node_idx, samples):
    """Recursively update costs of all descendant nodes."""
    current_sample = samples[node_idx]

    for i, sample in enumerate(samples):
        if sample.parent == node_idx:
            # Update child's cost
            edge_cost = calculate_distance(current_sample.x, sample.x)
            sample.cost = current_sample.cost + edge_cost
            # Recursively update its descendants
            update_descendant_costs(i, samples)

# --- RRT Sampling ---
def getNextSample(dim, samples, stepsize):
    """Samples a random point and identifies the nearest node."""
    if not samples:
        return None
    x_rand = np.array([random.randint(0, d - 1) for d in dim])
    sid = NearestSample(samples, x_rand)
    if sid is None:
        return None
    return samples[sid], x_rand, sid

# --- Button Callback for Run/Pause ---
def bfunc(obj, ename):
    global timer_id
    if 'Run' in button.status():
        if timer_id == -1:
            timer_id = plt.timer_callback('create', dt=10)
            print('RRT running...')
    else:
        if timer_id != -1:
            plt.timer_callback('destroy', timer_id)
            timer_id = -1
            print('RRT paused.')
    button.switch()

# --- RRT Iteration Step (Uses the new robust collision check) ---
def doRRTIteration(event):
    global samples, points, edges, source_point_actor, destination, path_found, timer_id, button

    # Don't run if we're still setting points or if path is already found
    if setting_source or setting_destination or path_found:
        return

    # Don't run if destination is not set
    if destination is None:
        return

    map_height, map_width = img.shape
    result = getNextSample([map_width, map_height], samples, stepSize)
    if result is None:
        return
    
    x_nearest_sample, x_rand, sid_nearest = result
    x_nearest = x_nearest_sample.x
    
    # STEER from the nearest node towards the random point
    direction = x_rand - x_nearest
    norm = np.linalg.norm(direction)
    
    if norm < 1e-6:
        return
        
    direction = direction / norm
    dist = min(stepSize, norm)
    # Calculate the ideal new point and then snap it to the integer grid
    x_new = (x_nearest + direction * dist).astype(int)

    # Ensure new node is within bounds
    map_height, map_width = img.shape
    if not (0 <= x_new[0] < map_width and 0 <= x_new[1] < map_height):
        return
    # Ensure both endpoints are not on obstacles
    if img[x_new[1], x_new[0]] or img[x_nearest[1], x_nearest[0]]:
        return
    # Ensure the path does not cross any obstacle
    collision_result = Collision(img, x_nearest, x_new)
    if collision_result:
        # Debug: Print collision details occasionally
        if len(samples) % 50 == 0:  # Print every 50th attempt
            print(f"Collision detected: {x_nearest} -> {x_new}")
        return
    # RRT* LOGIC: Find best parent within search radius
    search_radius = calculate_search_radius(len(samples))
    best_parent_idx, best_cost = find_best_parent(x_new, samples, search_radius)

    # If no suitable parent found within radius, use nearest neighbor
    if best_parent_idx is None:
        edge_cost = calculate_distance(x_nearest, x_new)
        best_parent_idx = sid_nearest
        best_cost = x_nearest_sample.cost + edge_cost

    # Create and add the new sample with optimal parent and cost
    ns = Sample(x_new, best_parent_idx, best_cost)
    new_node_idx = len(samples)
    samples.append(ns)

    # RRT* REWIRING: Check if we can improve existing connections
    rewired_count = rewire_tree(new_node_idx, samples, search_radius)

    # Debug: Print successful additions occasionally
    if len(samples) % 100 == 0:  # Print every 100th successful addition
        print(f"Added node #{len(samples)}: {x_new} (cost: {best_cost:.1f}, rewired: {rewired_count})")

    # Check if we've reached the goal
    distance_to_goal = np.linalg.norm(x_new - destination)
    if distance_to_goal <= goal_threshold:
        # Goal reached! Stop the algorithm and highlight the path
        path_found = True

        # Stop the timer
        if timer_id != -1:
            plt.timer_callback('destroy', timer_id)
            timer_id = -1
            button.status(" Run ")

        # Trace back the successful path
        trace_successful_path(len(samples) - 1)  # Index of the goal node

        print(f"🎉 PATH FOUND! Distance to goal: {distance_to_goal:.1f} pixels")
        print(f"Path length: {len(successful_path)} nodes")
        return

    # Update Visualization
    points.vertices = np.vstack([points.vertices, [ns.x[0], ns.x[1], 0]])
    new_edge = vd.Line([ns.x[0], ns.x[1], 0], [x_nearest[0], x_nearest[1], 0], lw=2, c=tree_colors[tree_color_idx])
    edges.append(new_edge)
    plt.add(points)
    if show_edges:
        plt.add(new_edge)
    if source_point_actor:
        plt.remove(source_point_actor)
        plt.add(source_point_actor)
    plt.render()

# --- Path Tracing and Highlighting ---
def trace_successful_path(goal_node_index):
    """Trace back from goal node to source and highlight the successful path."""
    global successful_path

    successful_path.clear()
    current_index = goal_node_index
    total_cost = 0.0

    # Trace back through parent indices
    while current_index is not None:
        current_sample = samples[current_index]
        successful_path.append(current_sample.x)
        total_cost = current_sample.cost  # Cost accumulates from source
        current_index = current_sample.parent

    # Reverse to get path from source to goal
    successful_path.reverse()

    # Create and add highlighted path visualization
    highlight_successful_path()

    # Print path statistics
    print(f"🎯 Optimal path cost: {total_cost:.2f} pixels")
    print(f"📏 Path segments: {len(successful_path) - 1}")

    return total_cost

def highlight_successful_path():
    """Create visual representation of the successful path."""
    if len(successful_path) < 2:
        return

    # Create thick green lines for the successful path
    for i in range(len(successful_path) - 1):
        start_point = successful_path[i]
        end_point = successful_path[i + 1]

        # Create a thick green line for the path
        path_edge = vd.Line([start_point[0], start_point[1], 0],
                           [end_point[0], end_point[1], 0],
                           lw=6, c='green')
        plt.add(path_edge)

    # Add green circles at path nodes
    path_points = np.array([[p[0], p[1], 0] for p in successful_path])
    path_nodes = vd.Points(path_points, r=6, c='green')
    plt.add(path_nodes)

    plt.render()

# --- Utility and GUI Button Functions ---
def reset_tree(obj=None, ename=None):
    global samples, points, edges, setting_source, setting_destination, source_point_actor
    global destination_point_actor, timer_id, destination, path_found, successful_path
    if timer_id != -1:
        plt.timer_callback('destroy', timer_id)
        timer_id = -1
        button.status(" Run ")
        print('RRT paused.')

    # Reset tree data
    samples = [Sample(source, None, 0.0)]  # Source has cost 0
    points = vd.Points([[source[0], source[1], 0]], r=8)
    edges.clear()
    successful_path.clear()

    # Remove old visual elements
    plt.remove('Line', 'Points')

    if source_point_actor:
        plt.remove(source_point_actor)
    if destination_point_actor:
        plt.remove(destination_point_actor)

    # Reset state variables
    setting_source = True
    setting_destination = False
    destination = None
    path_found = False

    # Create new source point actor
    source_point_actor = vd.Point([source[0], source[1], 0], r=14, c='black')
    plt.add(source_point_actor, points)

    plt.render()
    print("Tree reset. Left-click to set new source point.")

# --- Visualization Enhancements ---
tree_colors = ['red', 'blue', 'green']
tree_color_idx = 0
show_edges = True

def toggle_tree_color(obj, ename):
    global tree_color_idx
    tree_color_idx = (tree_color_idx + 1) % len(tree_colors)
    for edge in edges:
        edge.c(tree_colors[tree_color_idx])
    if show_edges:
        plt.add(edges)
    plt.render()
    print(f"Tree color set to: {tree_colors[tree_color_idx]}")

def toggle_tree_visibility(obj, ename):
    global show_edges
    show_edges = not show_edges
    if show_edges:
        plt.add(edges)
        obj.status("Hide Tree")
        print("Tree edges: Visible")
    else:
        plt.remove(edges)
        obj.status("Show Tree")
        print("Tree edges: Hidden")
    plt.render()

# --- Main GUI and State Initialization ---
imagePath = 'obstacle_map.png'

if not os.path.exists(imagePath):
    dummy_img = Image.new('L', (400, 400), color=255)
    from PIL import ImageDraw
    draw = ImageDraw.Draw(dummy_img)
    draw.rectangle([20, 20, 80, 150], fill=0)
    draw.rectangle([100, 80, 140, 250], fill=0)
    draw.rectangle([200, 20, 240, 100], fill=0)
    draw.rectangle([280, 150, 320, 380], fill=0)
    draw.rectangle([340, 50, 380, 200], fill=0)
    draw.rectangle([160, 280, 220, 350], fill=0)
    draw.ellipse([40, 250, 120, 320], fill=0)
    draw.ellipse([230, 180, 310, 280], fill=0)
    dummy_img.save(imagePath)
    print(f"Created a dummy obstacle map at {imagePath}")


img_pil = Image.open(imagePath).convert('L')
# Convert PIL image to numpy array
img_np = np.array(img_pil)

# Process the image: White (255) is free space, Black (0) is obstacle
# We want True=Obstacle, False=Free Space for our collision detection
# Since black pixels (0) are obstacles, we create a boolean array where 0 becomes True
img = img_np < 128  # Pixels darker than 128 are considered obstacles

# For display, we want obstacles to be black.
# img is True for obstacles. (1-img) is 0 for obstacles (black).
img_rgb = np.stack([(1 - img).astype(np.uint8) * 255] * 3, axis=-1)
img_actor = vd.Image(img_rgb)

# --- Initial State ---
source = np.array([10, 10])
destination = None  # Will be set by right-click
samples = [Sample(source, None, 0.0)]  # Source has cost 0
points = vd.Points([[source[0], source[1], 0]], r=8)
edges = []
stepSize = 20
setting_source = True
setting_destination = False
source_point_actor = None
destination_point_actor = None
timer_id = -1
draw_mode = False
brush_radius = 5
goal_threshold = 15  # Distance threshold to consider goal reached
path_found = False
successful_path = []

# --- RRT* Parameters ---
gamma = 50.0  # Scaling factor for search radius
max_radius = 50.0  # Maximum search radius
dimension = 2  # 2D space

# --- GUI Setup (will be initialized in main) ---
plt = None

def on_slider(widget, event):
    global stepSize
    stepSize = int(widget.value)
    widget.title = f"Step Size ({stepSize})"

def on_click(evt):
    global source, destination, samples, points, edges, setting_source, setting_destination
    global plt, source_point_actor, destination_point_actor, path_found

    if evt.picked3d is None:
        return

    map_height, map_width = img.shape
    x, y = int(evt.picked3d[0]), int(evt.picked3d[1])

    if not (0 <= x < map_width and 0 <= y < map_height):
        print(f"Click out of bounds.")
        return

    # Check if clicking on obstacle
    if img[y, x]:
        if setting_source:
            print("Cannot set source on an obstacle.")
        elif setting_destination:
            print("Cannot set destination on an obstacle.")
        return

    # For now, we'll use a simpler approach: left-click sets source, then destination
    # Since detecting right-click is complex in vedo, we'll use sequential left-clicks
    if setting_source:
        source = np.array([x, y])
        reset_tree() # Use reset_tree to handle all state changes
        setting_source = False
        setting_destination = True
        print(f"Source set to {source}. Left-click again to set destination.")

    elif setting_destination:
        destination = np.array([x, y])

        # Remove old destination actor if exists
        if destination_point_actor:
            plt.remove(destination_point_actor)

        # Create and add destination point actor
        destination_point_actor = vd.Point([destination[0], destination[1], 0], r=14, c='red')
        plt.add(destination_point_actor)
        plt.render()

        setting_destination = False
        path_found = False
        print(f"Destination set to {destination}. Press 'Run' or 'R' to start RRT path planning.")

    # Handle clicks when both points are set
    else:
        print("Both source and destination are set. Use 'Reset' to set new points.")

def draw_obstacle(evt):
    global img, img_actor, plt
    # Check if draw mode is enabled and we have a valid pick point
    if not draw_mode or evt.picked3d is None:
        return
    # Check if it's a left mouse button event (button 0) and if available
    if hasattr(evt, 'button') and evt.button != 0:
        return

    x, y = int(evt.picked3d[0]), int(evt.picked3d[1])
    h, w = img.shape
    if not (0 <= x < w and 0 <= y < h): return
    yy, xx = np.ogrid[:h, :w]
    mask = (xx - x)**2 + (yy - y)**2 <= brush_radius**2
    img[mask] = True  # Set pixels to obstacle
    # Check if it's a left mouse button event (button 0) and if available
    new_img_actor = vd.Image(img_rgb)
    plt.remove(img_actor)
    img_actor = new_img_actor
    plt += img_actor
    plt.render()

def on_keypress(evt):
    global brush_radius
    if hasattr(evt, 'keyPressed'):
        key = evt.keyPressed.lower()
        if key == 'r': bfunc(button, None)
        elif key == '=' or key == '+':
            brush_radius = min(brush_radius + 2, 50)
            print(f"Brush radius: {brush_radius}")
        elif key == '-':
            brush_radius = max(1, brush_radius - 2)
            print(f"Brush radius: {brush_radius}")

def toggle_draw_mode(obj, ename):
    global draw_mode
    draw_mode = not draw_mode
    print(f"Draw mode {'ON' if draw_mode else 'OFF'}.")
    obj.switch()

if __name__ == "__main__":
    # Initialize GUI
    plt = vd.Plotter(title="Interactive RRT* Planner")
    plt += img_actor
    plt.user_mode('2d')

    # Add GUI Elements and Callbacks to Plotter
    button = plt.add_button(bfunc, states=[" Run ", "Pause"], pos=(0.05, 0.34), size=24)
    plt.add_button(reset_tree, pos=(0.05, 0.28), size=24, states=["Reset"])
    plt.add_button(toggle_tree_color, pos=(0.05, 0.42), size=24, states=["Color"])
    plt.add_button(toggle_tree_visibility, pos=(0.05, 0.50), size=24, states=["Hide Tree", "Show Tree"])
    plt.add_button(toggle_draw_mode, pos=(0.05, 0.58), size=24, states=["Draw OFF", "Draw ON "])
    plt.add_slider(on_slider, 1, 100, value=stepSize, pos=((0.1, 0.9), (0.4, 0.9)),
                   title=f"Step Size ({stepSize})", show_value=False)

    plt.add_callback("mouse click", on_click)
    plt.add_callback("mouse move", draw_obstacle)
    plt.add_callback("timer", doRRTIteration)
    plt.add_callback("key press", on_keypress)
    # Final setup and display
    reset_tree() # Initialize the scene correctly
    print("🚀 RRT Path Planning Experiment")
    print("=" * 40)
    print("INSTRUCTIONS:")
    print("1. FIRST CLICK: Set SOURCE point (black dot) on free space")
    print("2. SECOND CLICK: Set DESTINATION point (red dot) on free space")
    print("3. Press 'Run' button or 'R' key to start RRT path planning")
    print("4. Algorithm will stop when path to destination is found")
    print("5. Use 'Reset' button to clear and start over")
    print("")
    print("CONTROLS:")
    print("- '+'/'-' keys: Change brush size for drawing obstacles")
    print("- Draw button: Toggle obstacle drawing mode")
    print("- Color button: Change tree color")
    print("- Hide/Show Tree: Toggle tree visibility")
    print("")
    plt.show(zoom="tightest").close()