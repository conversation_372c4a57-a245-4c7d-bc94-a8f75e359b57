from scipy.spatial import KD<PERSON>ree
import numpy as np
import math
import random
import vedo as vd
from vedo.pyplot import DirectedGraph
import os
from PIL import Image
from vedo import Text2D

# vedo backend setting
vd.settings.default_backend = 'vtk'

# --- Sample Class ---
class Sample:
    def __init__(self, x, parent=None):
        self.x = x  # Node position (2D numpy array)
        self.parent = parent  # Index of parent node

# --- KDTree Nearest Neighbor ---
def NearestSample(samples, x):
    """Find the nearest sample in the tree to point x."""
    if not samples:
        return None
    tree = KDTree([sample.x for sample in samples])
    dist, sid = tree.query(x)
    return sid

# --- NEW: ROBUST LINE RASTERIZATION ---
def get_line_pixels(p1, p2):
    """
    Get all unique pixels on the discrete grid line from p1 to p2.
    Uses a DDA (Digital Differential Analyzer) like algorithm for full coverage.
    p1, p2 are 2-element numpy arrays (x, y).
    """
    x1, y1 = p1[0], p1[1]
    x2, y2 = p2[0], p2[1]

    pixels = []
    dx = x2 - x1
    dy = y2 - y1

    # Determine the number of steps for interpolation
    steps = int(max(abs(dx), abs(dy)))
    
    # Handle the case where the distance is less than 1 pixel
    if steps == 0:
        steps = 1

    x_inc = dx / steps
    y_inc = dy / steps

    x = float(x1)
    y = float(y1)

    # Iterate to find all pixels on the line
    for _ in range(steps + 1):
        pixels.append([round(x), round(y)])
        x += x_inc
        y += y_inc
        
    # Return unique pixels to avoid checking the same pixel multiple times
    return np.unique(np.array(pixels, dtype=int), axis=0)

# --- REWRITTEN: Collision Checking Using Line Rasterization ---
def Collision(img, x1, x2):
    """
    Check if the line segment from x1 to x2 collides with obstacles in img.
    This version is robust and checks every pixel along the line's path.
    """
    height, width = img.shape
    
    # Get all pixels that the line from x1 to x2 touches
    line_pixels = get_line_pixels(x1, x2)
    
    for p in line_pixels:
        px, py = p[0], p[1]
        
        # Check 1: Is the pixel outside the map boundaries?
        if px < 0 or px >= width or py < 0 or py >= height:
            return True  # Path goes out of bounds, this is a collision

        # Check 2: Is the pixel on an obstacle? (img is indexed [y, x])
        if img[py, px]:
            return True  # Path collides with an obstacle
            
    return False  # No collision found

# --- RRT Sampling ---
def getNextSample(dim, samples, stepsize):
    """Samples a random point and identifies the nearest node."""
    if not samples:
        return None
    x_rand = np.array([random.randint(0, d - 1) for d in dim])
    sid = NearestSample(samples, x_rand)
    if sid is None:
        return None
    return samples[sid], x_rand, sid

# --- Button Callback for Run/Pause ---
def bfunc(obj, ename):
    global timer_id
    if 'Run' in button.status():
        if timer_id == -1:
            timer_id = plt.timer_callback('create', dt=10)
            print('RRT running...')
    else:
        if timer_id != -1:
            plt.timer_callback('destroy', timer_id)
            timer_id = -1
            print('RRT paused.')
    button.switch()

# --- RRT Iteration Step (Uses the new robust collision check) ---
def doRRTIteration(event):
    global samples, points, edges, source_point_actor
    if setting_source:
        return

    map_height, map_width = img.shape
    result = getNextSample([map_width, map_height], samples, stepSize)
    if result is None:
        return
    
    x_nearest_sample, x_rand, sid_nearest = result
    x_nearest = x_nearest_sample.x
    
    # STEER from the nearest node towards the random point
    direction = x_rand - x_nearest
    norm = np.linalg.norm(direction)
    
    if norm < 1e-6:
        return
        
    direction = direction / norm
    dist = min(stepSize, norm)
    # Calculate the ideal new point and then snap it to the integer grid
    x_new = (x_nearest + direction * dist).astype(int)

    # Ensure new node is within bounds
    map_height, map_width = img.shape
    if not (0 <= x_new[0] < map_width and 0 <= x_new[1] < map_height):
        return
    # Ensure both endpoints are not on obstacles
    if img[x_new[1], x_new[0]] or img[x_nearest[1], x_nearest[0]]:
        return
    # Ensure the path does not cross any obstacle
    if Collision(img, x_nearest, x_new):
        return
    # If the path is clear, create and add the new sample
    ns = Sample(x_new, sid_nearest)
    samples.append(ns)
    # Update Visualization
    points.vertices = np.vstack([points.vertices, [ns.x[0], ns.x[1], 0]])
    new_edge = vd.Line([ns.x[0], ns.x[1], 0], [x_nearest[0], x_nearest[1], 0], lw=2, c=tree_colors[tree_color_idx])
    edges.append(new_edge)
    plt.add(points)
    if show_edges:
        plt.add(new_edge)
    if source_point_actor:
        plt.remove(source_point_actor)
        plt.add(source_point_actor)
    plt.render()

# --- Utility and GUI Button Functions ---
def reset_tree():
    global samples, points, edges, setting_source, source_point_actor, timer_id
    if timer_id != -1:
        plt.timer_callback('destroy', timer_id)
        timer_id = -1
        button.status(" Run ")
        print('RRT paused.')

    samples = [Sample(source)]
    points = vd.Points([[source[0], source[1], 0]], r=8)
    edges.clear()
    
    plt.remove('Line', 'Points')
    
    if source_point_actor:
        plt.remove(source_point_actor)
    source_point_actor = vd.Point([source[0], source[1], 0], r=14, c='black')
    plt.add(source_point_actor, points)

    plt.render()
    setting_source = True
    print("Tree reset. Click to set new source point.")

# --- Visualization Enhancements ---
tree_colors = ['red', 'blue', 'green']
tree_color_idx = 0
show_edges = True

def toggle_tree_color(obj, ename):
    global tree_color_idx
    tree_color_idx = (tree_color_idx + 1) % len(tree_colors)
    for edge in edges:
        edge.c(tree_colors[tree_color_idx])
    if show_edges:
        plt.add(edges)
    plt.render()
    print(f"Tree color set to: {tree_colors[tree_color_idx]}")

def toggle_tree_visibility(obj, ename):
    global show_edges
    show_edges = not show_edges
    if show_edges:
        plt.add(edges)
        obj.status("Hide Tree")
        print("Tree edges: Visible")
    else:
        plt.remove(edges)
        obj.status("Show Tree")
        print("Tree edges: Hidden")
    plt.render()

# --- Main GUI and State Initialization ---
imagePath = 'obstacle_map.png'

if not os.path.exists(imagePath):
    dummy_img = Image.new('L', (400, 400), color=255)
    from PIL import ImageDraw
    draw = ImageDraw.Draw(dummy_img)
    draw.rectangle([20, 20, 80, 150], fill=0)
    draw.rectangle([100, 80, 140, 250], fill=0)
    draw.rectangle([200, 20, 240, 100], fill=0)
    draw.rectangle([280, 150, 320, 380], fill=0)
    draw.rectangle([340, 50, 380, 200], fill=0)
    draw.rectangle([160, 280, 220, 350], fill=0)
    draw.ellipse([40, 250, 120, 320], fill=0)
    draw.ellipse([230, 180, 310, 280], fill=0)
    dummy_img.save(imagePath)
    print(f"Created a dummy obstacle map at {imagePath}")


img_pil = Image.open(imagePath).convert('L')
# Convert PIL image to numpy array for vedo.Image
img_np = np.array(img_pil)
# White (255) is free space, Black (0) is obstacle.
# .binarize() makes obstacles 0 and free space 255.
# .tonumpy().astype(bool) makes obstacles (0) False and free space (255) True.
# We need to invert this for our logic where True means obstacle.
img_bool = vd.Image(img_np).bw().binarize().tonumpy().astype(bool)
img = ~img_bool  # Invert: now True=Obstacle, False=Free Space

# For display, we want obstacles to be black.
# img is True for obstacles. (1-img) is 0 for obstacles (black).
img_rgb = np.stack([(1 - img).astype(np.uint8) * 255] * 3, axis=-1)
img_actor = vd.Image(img_rgb)

# --- Initial State ---
source = np.array([10, 10])
samples = [Sample(source)]
points = vd.Points([[source[0], source[1], 0]], r=8)
edges = []
stepSize = 20
setting_source = True
source_point_actor = None
timer_id = -1
draw_mode = False
brush_radius = 5

# --- Plotter Setup ---
plt = vd.Plotter(title="Interactive RRT Planner")
plt += img_actor
plt.user_mode('2d')

def on_slider(widget, event):
    global stepSize
    stepSize = int(widget.value)
    widget.title = f"Step Size ({stepSize})"

def on_click(evt):
    global source, samples, points, edges, setting_source, plt, source_point_actor
    if evt.picked3d is None: return
    map_height, map_width = img.shape
    x, y = int(evt.picked3d[0]), int(evt.picked3d[1])
    if not (0 <= x < map_width and 0 <= y < map_height):
        print(f"Click out of bounds.")
        return
    if setting_source:
        if img[y, x]:
            print("Cannot set source on an obstacle.")
            return
        source = np.array([x, y])
        reset_tree() # Use reset_tree to handle all state changes
        setting_source = False
        print(f"Source set to {source}. Press 'Run' or 'R' to start.")
    else:
        print("Source already set. Use 'Reset' to set a new one.")

def draw_obstacle(evt):
    global img, img_actor, plt
    if not draw_mode or evt.picked3d is None or not evt.isDragging or evt.button != 0:
        return
    x, y = int(evt.picked3d[0]), int(evt.picked3d[1])
    h, w = img.shape
    if not (0 <= x < w and 0 <= y < h): return
    yy, xx = np.ogrid[:h, :w]
    mask = (xx - x)**2 + (yy - y)**2 <= brush_radius**2
    img[mask] = True  # Set pixels to obstacle
    img_rgb = np.stack([(1 - img).astype(np.uint8) * 255] * 3, axis=-1)
    new_img_actor = vd.Image(img_rgb)
    plt.remove(img_actor)
    img_actor = new_img_actor
    plt += img_actor
    plt.render()

def on_keypress(evt):
    global brush_radius
    if hasattr(evt, 'keyPressed'):
        key = evt.keyPressed.lower()
        if key == 'r': bfunc(button, None)
        elif key == '=' or key == '+':
            brush_radius = min(brush_radius + 2, 50)
            print(f"Brush radius: {brush_radius}")
        elif key == '-':
            brush_radius = max(1, brush_radius - 2)
            print(f"Brush radius: {brush_radius}")

def toggle_draw_mode(obj, ename):
    global draw_mode
    draw_mode = not draw_mode
    print(f"Draw mode {'ON' if draw_mode else 'OFF'}.")
    obj.switch()

# --- Add GUI Elements and Callbacks to Plotter ---
button = plt.add_button(bfunc, states=[" Run ", "Pause"], pos=(0.05, 0.34), size=24)
plt.add_button(reset_tree, pos=(0.05, 0.28), size=24, states=["Reset"])
plt.add_button(toggle_tree_color, pos=(0.05, 0.42), size=24, states=["Color"])
plt.add_button(toggle_tree_visibility, pos=(0.05, 0.50), size=24, states=["Hide Tree", "Show Tree"])
plt.add_button(toggle_draw_mode, pos=(0.05, 0.58), size=24, states=["Draw OFF", "Draw ON "])
plt.add_slider(on_slider, 1, 100, value=stepSize, pos=((0.1, 0.9), (0.4, 0.9)),
               title=f"Step Size ({stepSize})", show_value=False)

plt.add_callback("mouse click", on_click)
plt.add_callback("mouse move", draw_obstacle)
plt.add_callback("timer", doRRTIteration)
plt.add_callback("key press", on_keypress)

# Final setup and display
reset_tree() # Initialize the scene correctly
print("Click on the map to set the source point.")
print("Use '+' and '-' to change the brush size.")
plt.show(zoom="tightest").close()